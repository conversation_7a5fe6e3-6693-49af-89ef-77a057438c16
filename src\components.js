import React, { useState, useEffect } from 'react';
import { <PERSON>R<PERSON>, Plus, Trash2, FileDown, Zap, BarChart2, DollarSign, TrendingUp, Target, BrainCircuit, ListChecks, Check, AlertTriangle, ShieldCheck, Sparkles, MessageCircle, Code } from 'lucide-react';
import { kaizenOperations, userProfileOperations } from './localStorageManager';

// --- Gemini API Helper ---
async function generateWithGemini(prompt) {
    const apiKey = "AIzaSyAWPuMiQp8ltD3_1v4scm6U-T522GgKWbo";
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${apiKey}`;

    const payload = {
        contents: [{ role: "user", parts: [{ text: prompt }] }],
        generationConfig: {
            temperature: 0.7,
            topK: 1,
            topP: 1,
            maxOutputTokens: 8192,
        },
    };

    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorBody = await response.json();
            console.error("Gemini API Error:", errorBody);
            throw new Error(`API request failed with status ${response.status}`);
        }

        const result = await response.json();

        if (result.candidates && result.candidates.length > 0 &&
            result.candidates[0].content && result.candidates[0].content.parts &&
            result.candidates[0].content.parts.length > 0) {
            return result.candidates[0].content.parts[0].text;
        } else {
            console.warn("Gemini API response is empty or has unexpected structure.", result);
            return null;
        }
    } catch (error) {
        console.error("Error calling Gemini API:", error);
        throw error;
    }
}

// --- UI Components ---
// AuthScreen removed - no authentication needed

export function SplashScreen() {
    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-blue-600 text-white relative">
            <Zap size={80} className="animate-pulse" />
            <h1 className="text-4xl font-bold mt-4">مسار الكايزن</h1>
            <p className="mt-2 text-lg">التحسين المستمر في متناول يدك</p>
            <div className="mt-8 spinner"></div>

            {/* Developer Info */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
                <div className="flex items-center gap-2 text-white/80 text-sm">
                    <Code size={16} />
                    <span>Code by: <span className="font-semibold">Eng. Kareem Waheeb</span></span>
                </div>
            </div>

            <style>{`
                .spinner {
                    border: 4px solid rgba(255, 255, 255, 0.3); border-radius: 50%;
                    border-top: 4px solid #fff; width: 40px; height: 40px;
                    animation: spin 1s linear infinite;
                }
                @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
            `}</style>
        </div>
    );
}

// --- User Profile Component ---
// UserProfileModal removed - simplified without authentication

export function Dashboard({ kaizens, isDataLoading, navigateTo }) {
    const WASTE_TYPES = {
        transport: "النقل", inventory: "المخزون", motion: "الحركة", waiting: "الانتظار",
        overproduction: "الإنتاج الزائد", overprocessing: "المعالجة الزائدة", defects: "العيوب",
    };
    const ICONS = {
        transport: '🚚', inventory: '📦', motion: '🏃', waiting: '⏳',
        overproduction: '🏭', overprocessing: '⚙️', defects: '🔧'
    };

    return (
        <div className="p-4">
            <header className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold text-blue-600">لوحة التحكم</h1>
                <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-lg">
                        <Zap size={20} className="text-blue-600"/>
                        <div className="text-sm text-blue-600 font-medium">
                            مسار الكايزن
                        </div>
                    </div>
                </div>
            </header>
            <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                    <p className="text-3xl font-bold text-blue-700">{kaizens.filter(k => k.status !== 'completed').length}</p>
                    <p className="text-sm text-blue-600">مشروع نشط</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                    <p className="text-3xl font-bold text-green-700">{kaizens.filter(k => k.status === 'completed').length}</p>
                    <p className="text-sm text-green-600">مشروع مكتمل</p>
                </div>
            </div>
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">مشاريع الكايزن</h2>
                <button onClick={() => navigateTo('new')} className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg shadow hover:bg-blue-700 transition-all transform hover:scale-105"><Plus size={18} /><span>إضافة مشروع</span></button>
            </div>
            {isDataLoading ? (
                <div className="text-center py-10">
                    <div className="spinner mx-auto" style={{borderTopColor: '#3b82f6'}}></div>
                    <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
                </div>
            ) : kaizens.length > 0 ? (
                <div className="space-y-3">
                    {kaizens.map(kaizen => (
                        <div key={kaizen.id} onClick={() => navigateTo('detail', kaizen)} className="bg-white border border-gray-200 p-4 rounded-lg shadow-sm hover:shadow-md hover:border-blue-500 cursor-pointer transition-all">
                            <div className="flex justify-between items-start">
                                <div className="w-4/5">
                                    <h3 className="font-bold truncate">{kaizen.problemStatement || "مشروع بدون عنوان"}</h3>
                                    <p className="text-sm text-gray-500 mt-1">{WASTE_TYPES[kaizen.wasteType]}</p>
                                </div>
                                <span className="text-4xl">{ICONS[kaizen.wasteType]}</span>
                            </div>
                             <div className="mt-2 text-xs text-gray-500">فريق العمل: {kaizen.teamMembers || "غير محدد"}</div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="text-center py-10 px-4 bg-gray-50 rounded-lg"><BarChart2 size={48} className="mx-auto text-gray-400" /><p className="mt-4 text-gray-600">لم تقم بإضافة أي مشاريع بعد.</p><p className="text-sm text-gray-500">انقر على "إضافة مشروع" لبدء رحلة التحسين!</p></div>
            )}

            {/* Developer Info */}
            <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="bg-blue-100 p-2 rounded-full">
                                <Code size={16} className="text-blue-600" />
                            </div>
                            <div>
                                <p className="text-sm font-medium text-gray-800">
                                    Code by: <span className="text-blue-600 font-semibold">Eng. Kareem Waheeb</span>
                                </p>
                                <p className="text-xs text-gray-500">Full Stack Developer</p>
                            </div>
                        </div>
                        <button
                            onClick={() => window.open('https://wa.me/201159296333', '_blank')}
                            className="flex items-center gap-2 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"
                            title="تواصل عبر الواتساب"
                        >
                            <MessageCircle size={16} />
                            <span className="text-sm font-medium">واتساب</span>
                        </button>
                    </div>
                </div>
            </div>

        </div>
    );
}

export function NewKaizenScreen({ onKaizenCreated, onBack }) {
    const [step, setStep] = useState(1);
    const [kaizenData, setKaizenData] = useState({
        problemStatement: '', wasteType: 'transport', department: '', teamMembers: '',
        rootCauseAnalysis: {
            fiveWhys: Array(5).fill(''),
            fishbone: { man: [], machine: [], method: [], material: [], measurement: [], environment: [] }
        },
        goal: '',
        cost: '', returnOnInvestment: '',
        countermeasures: [''], status: 'planning', progress: 25, tasks: [],
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState('');

    const handleNext = () => setStep(s => s + 1);
    const handlePrev = () => setStep(s => s - 1);
    const updateData = (key, value) => setKaizenData(prev => ({ ...prev, [key]: value }));

    const handleSubmit = () => {
        setIsSubmitting(true);
        setError('');

        if (!kaizenData.problemStatement || !kaizenData.department || !kaizenData.goal) {
            setError("يرجى ملء جميع الحقول الأساسية (المشكلة، القسم، الهدف).");
            setIsSubmitting(false);
            return;
        }

        try {
            const { data, error } = kaizenOperations.create(kaizenData);

            if (error) throw new Error(error);

            console.log('✅ Kaizen created successfully:', data);
            onKaizenCreated(data);
        } catch (err) {
            console.error("Error creating kaizen:", err);
            setError("حدث خطأ أثناء إنشاء المشروع. يرجى المحاولة مرة أخرى.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="p-4">
            <header className="flex items-center mb-4"><button onClick={onBack} className="p-2 rounded-full hover:bg-gray-200"><ArrowRight size={20} /></button><h1 className="text-xl font-bold mr-4">مشروع كايزن جديد</h1></header>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6"><div className="bg-blue-600 h-2.5 rounded-full transition-all duration-500" style={{ width: `${(step / 4) * 100}%` }}></div></div>
            {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4" role="alert">{error}</div>}
            <div className="animate-fade-in">
                {step === 1 && <Step1 data={kaizenData} updateData={updateData} onNext={handleNext} />}
                {step === 2 && <Step2 data={kaizenData} updateData={updateData} onNext={handleNext} onPrev={handlePrev} />}
                {step === 3 && <Step3 data={kaizenData} updateData={updateData} onNext={handleNext} onPrev={handlePrev} />}
                {step === 4 && <Step4 data={kaizenData} updateData={updateData} onPrev={handlePrev} onSubmit={handleSubmit} isSubmitting={isSubmitting} />}
            </div>
        </div>
    );
}

export const Step1 = ({ data, updateData, onNext }) => {
    const WASTE_TYPES = {
        transport: "النقل", inventory: "المخزون", motion: "الحركة", waiting: "الانتظار",
        overproduction: "الإنتاج الزائد", overprocessing: "المعالجة الزائدة", defects: "العيوب",
    };

    return (
        <div>
            <h2 className="text-lg font-semibold mb-2">الخطوة 1: تحديد المشكلة</h2><p className="text-sm text-gray-500 mb-4">صف المشكلة التي تواجهها بوضوح.</p>
            <div className="space-y-4">
                <label className="block"><span className="text-gray-700">بيان المشكلة</span><textarea value={data.problemStatement} onChange={(e) => updateData('problemStatement', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-200" rows="3" placeholder="مثال: تأخير تسليم الطلبات للعملاء بنسبة 20%"></textarea></label>
                <label className="block"><span className="text-gray-700">نوع الهدر (Muda)</span><select value={data.wasteType} onChange={(e) => updateData('wasteType', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-200">{Object.entries(WASTE_TYPES).map(([key, value]) => (<option key={key} value={key}>{value}</option>))}</select></label>
                <label className="block"><span className="text-gray-700">القسم/العملية</span><input type="text" value={data.department} onChange={(e) => updateData('department', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-200" placeholder="مثال: قسم التعبئة والتغليف" /></label>
                <label className="block"><span className="text-gray-700">أعضاء الفريق</span><input type="text" value={data.teamMembers} onChange={(e) => updateData('teamMembers', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-200" placeholder="اكتب الأسماء مفصولة بفاصلة" /></label>
            </div><button onClick={onNext} className="mt-6 w-full bg-blue-600 text-white py-2 px-4 rounded-lg shadow hover:bg-blue-700 transition">التالي</button>
        </div>
    );
};

export const Step2 = ({ data, updateData, onNext, onPrev }) => {
    const [tool, setTool] = useState('5whys');
    const [isGenerating, setIsGenerating] = useState(false);

    const handle5WhysChange = (index, value) => {
        const newWhys = [...data.rootCauseAnalysis.fiveWhys];
        newWhys[index] = value;
        updateData('rootCauseAnalysis', { ...data.rootCauseAnalysis, fiveWhys: newWhys });
    };

    const handleFishboneChange = (category, value) => {
        const newFishbone = { ...data.rootCauseAnalysis.fishbone, [category]: value };
        updateData('rootCauseAnalysis', { ...data.rootCauseAnalysis, fishbone: newFishbone });
    };

    const generateRootCauseAnalysis = async () => {
        setIsGenerating(true);
        try {
            const prompt = `أنت خبير في منهجية الكايزن وتحليل الأسباب الجذرية. المشكلة هي: "${data.problemStatement}" في قسم "${data.department}".

            ${tool === '5whys' ?
                'قم بإجراء تحليل 5 Whys (لماذا) لهذه المشكلة. اكتب 5 أسئلة "لماذا" متتالية مع إجاباتها بالعربية. كل سؤال يجب أن يحفر أعمق في السبب الجذري.' :
                'قم بتحليل هذه المشكلة باستخدام مخطط هيكل السمكة (Fishbone Diagram). اقترح أسباب محتملة في كل فئة: العاملين، الآلات، الطرق، المواد، القياس، البيئة. اكتب 2-3 أسباب لكل فئة بالعربية.'
            }

            الرجاء الإجابة بتنسيق JSON كالتالي:
            ${tool === '5whys' ?
                '{"fiveWhys": ["لماذا 1؟ - الإجابة", "لماذا 2؟ - الإجابة", "لماذا 3؟ - الإجابة", "لماذا 4؟ - الإجابة", "لماذا 5؟ - الإجابة"]}' :
                '{"fishbone": {"man": ["سبب 1", "سبب 2"], "machine": ["سبب 1", "سبب 2"], "method": ["سبب 1", "سبب 2"], "material": ["سبب 1", "سبب 2"], "measurement": ["سبب 1", "سبب 2"], "environment": ["سبب 1", "سبب 2"]}}'
            }`;

            const result = await generateWithGemini(prompt);

            if (result) {
                try {
                    // Extract JSON from the response
                    const jsonMatch = result.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        const parsedResult = JSON.parse(jsonMatch[0]);

                        if (tool === '5whys' && parsedResult.fiveWhys) {
                            updateData('rootCauseAnalysis', {
                                ...data.rootCauseAnalysis,
                                fiveWhys: parsedResult.fiveWhys
                            });
                        } else if (tool === 'fishbone' && parsedResult.fishbone) {
                            updateData('rootCauseAnalysis', {
                                ...data.rootCauseAnalysis,
                                fishbone: parsedResult.fishbone
                            });
                        }
                    }
                } catch (parseError) {
                    console.error("Error parsing AI response:", parseError);
                    alert("تم الحصول على اقتراحات من QLQ BOT ولكن حدث خطأ في التحليل. يرجى المحاولة مرة أخرى.");
                }
            }
        } catch (error) {
            console.error("Error generating root cause analysis:", error);
            alert("حدث خطأ في الاتصال بـ QLQ BOT. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.");
        } finally {
            setIsGenerating(false);
        }
    };

    return (
        <div>
            <div className="flex justify-between items-center mb-2">
                 <h2 className="text-lg font-semibold">الخطوة 2: تحليل الأسباب الجذرية</h2>
                <button onClick={generateRootCauseAnalysis} disabled={isGenerating} className="flex items-center gap-2 text-sm bg-purple-100 text-purple-700 px-3 py-1 rounded-lg hover:bg-purple-200 transition disabled:bg-gray-200">
                    <Sparkles size={16} className={isGenerating ? 'animate-spin' : ''} />
                    <span>{isGenerating ? 'جاري التفكير...' : '✨ اقترح أسبابًا'}</span>
                </button>
            </div>
            <p className="text-sm text-gray-500 mb-4">استخدم الأدوات التالية أو دع QLQ BOT يساعدك.</p>
            <div className="flex justify-center border-b border-gray-200 mb-4">
                <button onClick={() => setTool('5whys')} className={`px-4 py-2 text-sm font-medium ${tool === '5whys' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}>تحليل 5 Whys</button>
                <button onClick={() => setTool('fishbone')} className={`px-4 py-2 text-sm font-medium ${tool === 'fishbone' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}>مخطط هيكل السمكة</button>
            </div>
            {tool === '5whys' && (<div className="space-y-2 animate-fade-in">{data.rootCauseAnalysis.fiveWhys.map((why, index) => (<input key={index} type="text" value={why} onChange={(e) => handle5WhysChange(index, e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" placeholder={`لماذا ${index + 1}؟`}/>))}</div>)}
            {tool === 'fishbone' && <FishboneDiagramEditor data={data.rootCauseAnalysis.fishbone} onChange={handleFishboneChange} />}
            <div className="flex justify-between mt-6"><button onClick={onPrev} className="bg-gray-200 text-gray-800 py-2 px-4 rounded-lg">السابق</button><button onClick={onNext} className="bg-blue-600 text-white py-2 px-4 rounded-lg shadow">التالي</button></div>
        </div>
    );
};

export const FishboneDiagramEditor = ({ data, onChange }) => {
    const categories = { man: 'العاملين', machine: 'الآلات', method: 'الطرق', material: 'المواد', measurement: 'القياس', environment: 'البيئة'};
    const handleAddCause = (category) => { onChange(category, [...(data[category] || []), '']); };
    const handleCauseChange = (category, index, value) => {
        const newCauses = [...data[category]];
        newCauses[index] = value;
        onChange(category, newCauses);
    };
    return (
        <div className="space-y-4 animate-fade-in">
            {Object.entries(categories).map(([key, label]) => (
                <div key={key}>
                    <label className="font-semibold">{label}</label>
                    {(data[key] || []).map((cause, index) => (
                        <input key={index} type="text" value={cause} onChange={(e) => handleCauseChange(key, index, e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" placeholder="اكتب سببًا محتملًا"/>
                    ))}
                    <button onClick={() => handleAddCause(key)} className="mt-1 text-sm text-blue-600 hover:underline">+ إضافة سبب</button>
                </div>
            ))}
        </div>
    );
};

export const Step3 = ({ data, updateData, onNext, onPrev }) => (
    <div>
        <h2 className="text-lg font-semibold mb-2">الخطوة 3: الأهداف والجوانب المالية</h2><p className="text-sm text-gray-500 mb-4">حدد هدفًا ذكيًا (SMART) وقدر التكلفة والعائد.</p>
        <div className="space-y-4">
            <label className="block"><span className="text-gray-700">الهدف المرجو (SMART Goal)</span><input type="text" value={data.goal} onChange={(e) => updateData('goal', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" placeholder="مثال: تقليل وقت التسليم إلى أقل من 24 ساعة" /></label>
            <label className="block"><span className="text-gray-700">التكلفة التقديرية (بالعملة المحلية)</span><input type="number" value={data.cost} onChange={(e) => updateData('cost', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" placeholder="0.00" /></label>
            <label className="block"><span className="text-gray-700">العائد المالي المتوقع (بالعملة المحلية)</span><input type="number" value={data.returnOnInvestment} onChange={(e) => updateData('returnOnInvestment', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500" placeholder="0.00" /></label>
        </div>
        <div className="flex justify-between mt-6"><button onClick={onPrev} className="bg-gray-200 text-gray-800 py-2 px-4 rounded-lg">السابق</button><button onClick={onNext} className="bg-blue-600 text-white py-2 px-4 rounded-lg shadow">التالي</button></div>
    </div>
);

export const Step4 = ({ data, updateData, onPrev, onSubmit, isSubmitting }) => {
    const [isGenerating, setIsGenerating] = useState(false);

    const handleCountermeasureChange = (index, value) => {
        const newCountermeasures = [...data.countermeasures];
        newCountermeasures[index] = value;
        updateData('countermeasures', newCountermeasures);
    };

    const addCountermeasure = () => {
        updateData('countermeasures', [...(data.countermeasures || []), '']);
    };

    const generateCountermeasures = async () => {
        setIsGenerating(true);
        try {
            const rootCauses = data.rootCauseAnalysis.fiveWhys.filter(why => why.trim() !== '').join(', ');
            const fishboneCauses = Object.values(data.rootCauseAnalysis.fishbone).flat().filter(cause => cause.trim() !== '').join(', ');

            const prompt = `أنت خبير في منهجية الكايزن وحل المشاكل. المشكلة هي: "${data.problemStatement}" في قسم "${data.department}".

            الهدف المطلوب: "${data.goal}"

            الأسباب الجذرية المحددة:
            - من تحليل 5 Whys: ${rootCauses}
            - من مخطط هيكل السمكة: ${fishboneCauses}

            اقترح 4-6 إجراءات تصحيحية عملية وقابلة للتطبيق لحل هذه المشكلة. يجب أن تكون الحلول:
            1. محددة وواضحة
            2. قابلة للقياس
            3. قابلة للتحقيق
            4. ذات صلة بالمشكلة
            5. محددة بوقت

            الرجاء الإجابة بتنسيق JSON كالتالي:
            {"countermeasures": ["إجراء 1", "إجراء 2", "إجراء 3", "إجراء 4", "إجراء 5", "إجراء 6"]}`;

            const result = await generateWithGemini(prompt);

            if (result) {
                try {
                    // Extract JSON from the response
                    const jsonMatch = result.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        const parsedResult = JSON.parse(jsonMatch[0]);

                        if (parsedResult.countermeasures && Array.isArray(parsedResult.countermeasures)) {
                            updateData('countermeasures', parsedResult.countermeasures);
                        }
                    }
                } catch (parseError) {
                    console.error("Error parsing AI response:", parseError);
                    alert("تم الحصول على اقتراحات من QLQ BOT ولكن حدث خطأ في التحليل. يرجى المحاولة مرة أخرى.");
                }
            }
        } catch (error) {
            console.error("Error generating countermeasures:", error);
            alert("حدث خطأ في الاتصال بـ QLQ BOT. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.");
        } finally {
            setIsGenerating(false);
        }
    };

    return (
        <div>
             <div className="flex justify-between items-center mb-2">
                <h2 className="text-lg font-semibold">الخطوة 4: الإجراءات المقترحة</h2>
                <button onClick={generateCountermeasures} disabled={isGenerating} className="flex items-center gap-2 text-sm bg-purple-100 text-purple-700 px-3 py-1 rounded-lg hover:bg-purple-200 transition disabled:bg-gray-200">
                    <Sparkles size={16} className={isGenerating ? 'animate-spin' : ''} />
                    <span>{isGenerating ? 'جاري الإبداع...' : '✨ اقترح حلولاً'}</span>
                </button>
            </div>
            <p className="text-sm text-gray-500 mb-4">اكتب الحلول التي ستطبقها، أو دع QLQ BOT يقترح عليك.</p>
            <div className="space-y-2">
                {(data.countermeasures || []).map((cm, index) => (
                    <input key={index} type="text" value={cm} onChange={(e) => handleCountermeasureChange(index, e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" placeholder={`إجراء مقترح ${index + 1}`}/>
                ))}
            </div>
            <button onClick={addCountermeasure} className="mt-2 text-sm text-blue-600 hover:underline">+ إضافة إجراء آخر</button>
            <div className="flex justify-between mt-6"><button onClick={onPrev} className="bg-gray-200 text-gray-800 py-2 px-4 rounded-lg">السابق</button><button onClick={onSubmit} disabled={isSubmitting} className="bg-green-600 text-white py-2 px-4 rounded-lg shadow disabled:bg-green-300">{isSubmitting ? 'جاري الإنشاء...' : 'إنشاء المشروع'}</button></div>
        </div>
    );
};

export function KaizenDetailScreen({ kaizen, onBack, navigateTo, onDelete }) {
    const [localKaizen, setLocalKaizen] = useState(kaizen);
    const WASTE_TYPES = {
        transport: "النقل", inventory: "المخزون", motion: "الحركة", waiting: "الانتظار",
        overproduction: "الإنتاج الزائد", overprocessing: "المعالجة الزائدة", defects: "العيوب",
    };

    const updateKaizenField = (field, value) => {
        const updatedKaizen = { ...localKaizen, [field]: value };
        setLocalKaizen(updatedKaizen);

        if (!kaizen?.id) return;

        try {
            const { error } = kaizenOperations.update(kaizen.id, { [field]: value });

            if (error) throw new Error(error);

            console.log('✅ Kaizen field updated successfully:', field, value);
        } catch (error) {
            console.error("Error updating kaizen:", error);
            // Revert local state on error
            setLocalKaizen(kaizen);
        }
    };

    return (
        <div className="p-4 pb-16">
            <header className="flex justify-between items-center mb-4">
                <button onClick={onBack} className="p-2 rounded-full hover:bg-gray-200"><ArrowRight size={20} /></button>
                <div className="flex gap-2">
                    {localKaizen.status !== 'completed' && (
                        <button
                            onClick={() => {
                                // eslint-disable-next-line no-restricted-globals
                                if (confirm('هل تريد إكمال هذا المشروع؟ سيتم تحديث حالته إلى "مكتمل".')) {
                                    updateKaizenField('status', 'completed');
                                }
                            }}
                            className="flex items-center gap-2 bg-green-600 text-white px-3 py-2 rounded-lg shadow hover:bg-green-700 transition-all"
                            title="إكمال المشروع"
                        >
                            <Check size={16} />
                            <span className="hidden sm:inline">إكمال</span>
                        </button>
                    )}
                    <button onClick={() => onDelete(kaizen.id)} className="p-2 rounded-full hover:bg-red-100 text-red-600"><Trash2 size={20} /></button>
                    <button onClick={() => navigateTo('a3', localKaizen)} className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg shadow hover:bg-blue-700"><FileDown size={18} /><span>عرض تقرير A3</span></button>
                </div>
            </header>
            <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <h1 className="text-xl font-bold text-blue-800">{localKaizen.problemStatement}</h1>
                <p className="text-sm text-blue-600 mt-1">{WASTE_TYPES[localKaizen.wasteType]} - {localKaizen.department}</p>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-red-50 p-3 rounded-lg flex items-center gap-3"><DollarSign className="text-red-500" size={24} /><div className="text-red-700"><p className="font-bold">{Number(localKaizen.cost || 0).toLocaleString()} </p><p className="text-xs">التكلفة</p></div></div>
                <div className="bg-green-50 p-3 rounded-lg flex items-center gap-3"><TrendingUp className="text-green-500" size={24} /><div className="text-green-700"><p className="font-bold">{Number(localKaizen.returnOnInvestment || 0).toLocaleString()} </p><p className="text-xs">العائد</p></div></div>
            </div>
            <div className="space-y-6">
                <KaizenSection title="الهدف (PLAN)"><p>{localKaizen.goal}</p></KaizenSection>
                <KaizenSection title="الإجراءات التنفيذية (DO)"><KanbanBoard tasks={localKaizen.tasks || []} updateTasks={(tasks) => updateKaizenField('tasks', tasks)} /></KaizenSection>
                <KaizenSection title="التحقق من النتائج (CHECK)"><textarea value={localKaizen.results || ''} onChange={(e) => updateKaizenField('results', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" rows="3" placeholder="سجل هنا النتائج والمقاييس..."/></KaizenSection>
                <KaizenSection title="التوحيد القياسي (ACT)"><textarea value={localKaizen.standardization || ''} onChange={(e) => updateKaizenField('standardization', e.target.value)} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm" rows="3" placeholder="وثّق هنا الإجراءات الجديدة..."/></KaizenSection>
            </div>
        </div>
    );
}

export const KaizenSection = ({ title, children }) => (<div><h2 className="text-lg font-semibold text-gray-700 border-b-2 border-gray-200 pb-1 mb-3">{title}</h2><div className="text-gray-800 text-base">{children}</div></div>);

export function KanbanBoard({ tasks, updateTasks }) {
    const [newTask, setNewTask] = useState('');
    const [showTaskModal, setShowTaskModal] = useState(false);
    const [editingTask, setEditingTask] = useState(null);
    const [taskForm, setTaskForm] = useState({ text: '', assignedTo: '', dueDate: '', priority: 'medium' });

    const handleAddTask = () => {
        if (newTask.trim() === '') return;
        updateTasks([...tasks, {
            id: Date.now(),
            text: newTask.trim(),
            status: 'todo',
            assignedTo: '',
            dueDate: '',
            priority: 'medium',
            completedBy: '',
            completedAt: ''
        }]);
        setNewTask('');
    };

    const moveTask = (taskId, newStatus) => {
        const updatedTasks = tasks.map(t => {
            if (t.id === taskId) {
                const updatedTask = { ...t, status: newStatus };
                if (newStatus === 'done' && !t.completedAt) {
                    updatedTask.completedAt = new Date().toISOString();
                }
                return updatedTask;
            }
            return t;
        });
        updateTasks(updatedTasks);
    };

    const openTaskModal = (task = null) => {
        if (task) {
            setEditingTask(task);
            setTaskForm({
                text: task.text,
                assignedTo: task.assignedTo || '',
                dueDate: task.dueDate || '',
                priority: task.priority || 'medium'
            });
        } else {
            setEditingTask(null);
            setTaskForm({ text: '', assignedTo: '', dueDate: '', priority: 'medium' });
        }
        setShowTaskModal(true);
    };

    const saveTask = () => {
        if (!taskForm.text.trim()) return;

        if (editingTask) {
            // Update existing task
            updateTasks(tasks.map(t =>
                t.id === editingTask.id
                    ? { ...t, ...taskForm }
                    : t
            ));
        } else {
            // Add new task
            updateTasks([...tasks, {
                id: Date.now(),
                status: 'todo',
                completedBy: '',
                completedAt: '',
                ...taskForm
            }]);
        }
        setShowTaskModal(false);
    };

    const markCompleted = (taskId, completedBy) => {
        updateTasks(tasks.map(t =>
            t.id === taskId
                ? {
                    ...t,
                    status: 'done',
                    completedBy: completedBy || 'غير محدد',
                    completedAt: new Date().toISOString()
                }
                : t
        ));
    };

    const deleteTask = (taskId) => {
        updateTasks(tasks.filter(t => t.id !== taskId));
    };

    const columns = {
        todo: { title: 'المهام', color: 'bg-gray-200', tasks: tasks.filter(t => t.status === 'todo') },
        doing: { title: 'قيد التنفيذ', color: 'bg-yellow-200', tasks: tasks.filter(t => t.status === 'doing') },
        done: { title: 'مكتمل', color: 'bg-green-200', tasks: tasks.filter(t => t.status === 'done') }
    };

    const getPriorityColor = (priority) => {
        switch(priority) {
            case 'high': return 'border-l-4 border-red-500';
            case 'medium': return 'border-l-4 border-yellow-500';
            case 'low': return 'border-l-4 border-green-500';
            default: return '';
        }
    };

    return (
        <div>
            <div className="flex gap-2 mb-4">
                <input
                    type="text"
                    value={newTask}
                    onChange={e => setNewTask(e.target.value)}
                    placeholder="إضافة مهمة سريعة..."
                    className="flex-grow rounded-md border-gray-300 shadow-sm"
                />
                <button onClick={handleAddTask} className="bg-blue-500 text-white px-3 rounded-md">إضافة</button>
                <button onClick={() => openTaskModal()} className="bg-green-500 text-white px-3 rounded-md">+ تفصيلي</button>
            </div>

            <div className="grid grid-cols-3 gap-2">
                {Object.entries(columns).map(([key, col]) => (
                    <div key={key} className="p-2 rounded-lg bg-gray-100">
                        <h3 className={`font-semibold text-sm p-1 rounded-md text-center ${col.color}`}>
                            {col.title} ({col.tasks.length})
                        </h3>
                        <div className="mt-2 space-y-2">
                            {col.tasks.map(task => (
                                <div key={task.id} className={`bg-white p-2 rounded shadow-sm text-sm ${getPriorityColor(task.priority)}`}>
                                    <p className="font-medium">{task.text}</p>
                                    {task.assignedTo && (
                                        <p className="text-xs text-blue-600 mt-1">👤 {task.assignedTo}</p>
                                    )}
                                    {task.dueDate && (
                                        <p className="text-xs text-gray-500">📅 {new Date(task.dueDate).toLocaleDateString('ar-EG')}</p>
                                    )}
                                    {task.completedBy && task.status === 'done' && (
                                        <div className="text-xs text-green-600 mt-1">
                                            <p>✅ أكمله: {task.completedBy}</p>
                                            <p>🕐 {new Date(task.completedAt).toLocaleDateString('ar-EG')}</p>
                                        </div>
                                    )}
                                    <div className="flex gap-1 mt-2 text-xs">
                                        <button onClick={() => openTaskModal(task)} className="hover:text-blue-600" title="تعديل">✏️</button>
                                        {key !== 'todo' && <button onClick={() => moveTask(task.id, 'todo')} className="hover:text-blue-600" title="إلى المهام">📋</button>}
                                        {key !== 'doing' && <button onClick={() => moveTask(task.id, 'doing')} className="hover:text-yellow-600" title="قيد التنفيذ">⏳</button>}
                                        {key !== 'done' && (
                                            <button
                                                onClick={() => {
                                                    const completedBy = prompt('من أكمل هذه المهمة؟');
                                                    if (completedBy !== null) markCompleted(task.id, completedBy);
                                                }}
                                                className="hover:text-green-600"
                                                title="إكمال"
                                            >
                                                ✅
                                            </button>
                                        )}
                                        <button onClick={() => deleteTask(task.id)} className="hover:text-red-600" title="حذف">🗑️</button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            </div>

            {/* Task Modal */}
            {showTaskModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md mx-4">
                        <h3 className="text-lg font-bold mb-4">
                            {editingTask ? 'تعديل المهمة' : 'إضافة مهمة جديدة'}
                        </h3>

                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium mb-1">وصف المهمة</label>
                                <textarea
                                    value={taskForm.text}
                                    onChange={(e) => setTaskForm({...taskForm, text: e.target.value})}
                                    className="w-full p-2 border rounded-md"
                                    rows="3"
                                    placeholder="اكتب وصف المهمة..."
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium mb-1">المسؤول عن التنفيذ</label>
                                <input
                                    type="text"
                                    value={taskForm.assignedTo}
                                    onChange={(e) => setTaskForm({...taskForm, assignedTo: e.target.value})}
                                    className="w-full p-2 border rounded-md"
                                    placeholder="اسم الشخص المسؤول"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium mb-1">تاريخ الاستحقاق</label>
                                <input
                                    type="date"
                                    value={taskForm.dueDate}
                                    onChange={(e) => setTaskForm({...taskForm, dueDate: e.target.value})}
                                    className="w-full p-2 border rounded-md"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium mb-1">الأولوية</label>
                                <select
                                    value={taskForm.priority}
                                    onChange={(e) => setTaskForm({...taskForm, priority: e.target.value})}
                                    className="w-full p-2 border rounded-md"
                                >
                                    <option value="low">منخفضة</option>
                                    <option value="medium">متوسطة</option>
                                    <option value="high">عالية</option>
                                </select>
                            </div>
                        </div>

                        <div className="flex gap-2 mt-6">
                            <button
                                onClick={saveTask}
                                className="flex-1 bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700"
                            >
                                {editingTask ? 'حفظ التغييرات' : 'إضافة المهمة'}
                            </button>
                            <button
                                onClick={() => setShowTaskModal(false)}
                                className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-md hover:bg-gray-400"
                            >
                                إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export function A3ReportScreen({ kaizen, onBack }) {
    const [userProfile, setUserProfile] = useState(null);

    const handlePrint = () => {
        // تحسين إعدادات الطباعة مع CSS محسن
        const printWindow = window.open('', '_blank');
        const content = document.getElementById('a3-report-content').outerHTML;

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير A3 - ${kaizen.problemStatement}</title>
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <script src="https://cdn.tailwindcss.com"></script>
                <style>
                    @page {
                        size: A3 landscape;
                        margin: 12mm;
                        orphans: 3;
                        widows: 3;
                    }

                    * {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }

                    body {
                        font-family: 'Cairo', sans-serif;
                        direction: rtl;
                        margin: 0;
                        padding: 0;
                        background: white;
                        color: #1a1a1a;
                        font-size: 11px;
                        line-height: 1.4;
                    }

                    .print-container {
                        width: 100%;
                        padding: 8mm;
                        box-sizing: border-box;
                        position: relative;
                    }

                    /* إظهار الشعار في الطباعة فقط */
                    .print-only {
                        display: block !important;
                    }

                    /* شعار الشركة */
                    .company-logo-header {
                        position: absolute;
                        top: 0;
                        left: 0;
                        padding: 8px;
                        z-index: 10;
                    }

                    .company-logo-img {
                        width: 80px;
                        height: 60px;
                        object-fit: contain;
                        border: 1px solid #ccc;
                        border-radius: 4px;
                        background: white;
                        padding: 3px;
                    }

                    .logo-box {
                        width: 80px;
                        height: 60px;
                        background: linear-gradient(135deg, #1e40af, #3b82f6);
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 8px;
                        font-weight: 600;
                        text-align: center;
                        border-radius: 4px;
                        padding: 3px;
                    }

                    .company-name {
                        font-size: 8px;
                        font-weight: 600;
                        color: #374151;
                        margin-top: 3px;
                        text-align: center;
                        line-height: 1.2;
                    }

                    /* رأس التقرير */
                    .a3-header {
                        margin-top: 0;
                        margin-bottom: 15px;
                        padding: 15px 0;
                        border-bottom: 2px solid #1e40af;
                        page-break-after: avoid;
                    }

                    .header-content {
                        margin-right: 100px;
                    }

                    .report-title {
                        font-size: 22px;
                        font-weight: 700;
                        color: #1e40af;
                        margin: 0 0 8px 0;
                        text-align: center;
                    }

                    .problem-title {
                        font-size: 14px;
                        font-weight: 600;
                        color: #374151;
                        margin: 0 0 10px 0;
                        text-align: center;
                        background: #f8fafc;
                        padding: 8px;
                        border-radius: 4px;
                        border-right: 3px solid #1e40af;
                    }

                    .info-grid {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 6px;
                    }

                    .info-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 5px 8px;
                        background: #f9fafb;
                        border-radius: 3px;
                        border-right: 2px solid #e5e7eb;
                    }

                    .info-label, .info-value {
                        font-size: 9px;
                    }

                    .info-label {
                        font-weight: 600;
                        color: #6b7280;
                    }

                    .info-value {
                        font-weight: 500;
                        color: #374151;
                    }

                    .status-badge {
                        padding: 2px 6px;
                        border-radius: 8px;
                        font-size: 8px;
                        font-weight: 600;
                    }

                    .status-completed {
                        background: #dcfce7;
                        color: #166534;
                    }

                    .status-active {
                        background: #dbeafe;
                        color: #1d4ed8;
                    }

                    /* خط فاصل */
                    .section-divider {
                        height: 1px;
                        background: #1e40af;
                        margin: 12px 0;
                    }

                    /* الأقسام */
                    .a3-section {
                        border: 1px solid #666;
                        border-radius: 4px;
                        margin-bottom: 8px;
                        background: white;
                        page-break-inside: avoid;
                        break-inside: avoid;
                        orphans: 3;
                        widows: 3;
                    }

                    .a3-section-title {
                        background: #f0f2f5;
                        color: #1e40af;
                        border-bottom: 1px solid #666;
                        padding: 8px 10px;
                        font-size: 11px;
                        font-weight: 700;
                        page-break-after: avoid;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                    }

                    .a3-section-content {
                        color: #1a1a1a;
                        padding: 10px;
                        font-size: 9px;
                        line-height: 1.3;
                    }

                    /* الشبكة */
                    .grid {
                        display: grid;
                    }

                    .md\\:grid-cols-2 {
                        grid-template-columns: 1fr 1fr;
                        gap: 8px;
                    }

                    /* النصوص */
                    .text-xs {
                        font-size: 8px;
                        line-height: 1.2;
                    }

                    .text-sm {
                        font-size: 9px;
                        line-height: 1.3;
                    }

                    .font-bold {
                        font-weight: 700;
                    }

                    .font-medium {
                        font-weight: 600;
                    }

                    /* المساحات */
                    .space-y-1 > * + * {
                        margin-top: 2px;
                    }

                    .space-y-2 > * + * {
                        margin-top: 4px;
                    }

                    .gap-2 {
                        gap: 4px;
                    }

                    .gap-4 {
                        gap: 6px;
                    }

                    .mt-1 { margin-top: 2px; }
                    .mt-2 { margin-top: 4px; }
                    .mt-3 { margin-top: 6px; }
                    .mt-4 { margin-top: 8px; }
                    .mt-6 { margin-top: 10px; }
                    .mb-1 { margin-bottom: 2px; }
                    .mb-2 { margin-bottom: 4px; }
                    .p-2 { padding: 3px; }
                    .rounded { border-radius: 2px; }

                    /* الألوان */
                    .bg-blue-50, .bg-green-50, .bg-yellow-50, .bg-gray-50 {
                        background-color: #f8f9fa;
                    }

                    .text-blue-600, .text-green-700, .text-yellow-700, .text-gray-600 {
                        color: #374151;
                    }

                    .text-blue-700, .text-red-700, .text-green-700 {
                        color: #1f2937;
                        font-weight: 600;
                    }

                    /* منع تقطيع العناصر */
                    .bg-blue-50, .bg-green-50, .bg-yellow-50, .bg-gray-50,
                    .info-item, .status-badge, .space-y-1, .space-y-2 {
                        page-break-inside: avoid;
                        break-inside: avoid;
                    }
                </style>
            </head>
            <body>
                <div class="print-container">
                    ${content}
                </div>
                <script>
                    window.onload = function() {
                        setTimeout(() => {
                            window.print();
                            window.onafterprint = function() {
                                window.close();
                            };
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `);

        printWindow.document.close();
    };

    useEffect(() => {
        fetchUserProfile();
    }, []);

    const fetchUserProfile = () => {
        try {
            const profile = userProfileOperations.get();
            setUserProfile(profile);
        } catch (error) {
            console.error('Error fetching user profile:', error);
        }
    };

    const A3Section = ({ icon, title, children }) => (
        <section className="a3-section">
            <h3 className="a3-section-title">
                {icon}
                <span>{title}</span>
            </h3>
            <div className="a3-section-content">{children}</div>
        </section>
    );

    return (
        <div className="a3-print-container">
            <header className="p-4 bg-white shadow-md flex justify-between items-center print:hidden">
                <button onClick={onBack} className="p-2 rounded-full hover:bg-gray-200"><ArrowRight size={20} /></button>
                <h1 className="text-xl font-bold">تقرير A3 احترافي</h1>
                <button onClick={handlePrint} className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg shadow hover:bg-blue-700 transition"><FileDown size={18} /><span>طباعة A3 Landscape</span></button>
            </header>

            <main className="p-4 bg-gray-100 print:bg-white">
                <div id="a3-report-content" className="bg-white p-8 rounded-lg shadow-lg max-w-4xl mx-auto font-serif" dir="rtl">
                    {/* شعار الشركة في أعلى اليسار - يظهر فقط في الطباعة */}
                    <div className="company-logo-header print-only">
                        {userProfile?.company_logo ? (
                            <div className="company-logo-container">
                                <img
                                    src={userProfile.company_logo}
                                    alt="شعار الشركة"
                                    className="company-logo-img"
                                />
                                {userProfile.company_name && (
                                    <p className="company-name">{userProfile.company_name}</p>
                                )}
                            </div>
                        ) : (
                            <div className="company-logo-placeholder">
                                <div className="logo-box">
                                    {userProfile?.company_name || 'شعار الشركة'}
                                </div>
                                {userProfile?.company_name && (
                                    <p className="company-name">{userProfile.company_name}</p>
                                )}
                            </div>
                        )}
                    </div>

                    {/* رأس التقرير */}
                    <header className="a3-header">
                        <div className="header-content">
                            <h1 className="report-title">تقرير A3 للتحسين المستمر</h1>
                            <h2 className="problem-title">{kaizen.problemStatement}</h2>

                            <div className="report-info">
                                <div className="info-grid">
                                    <div className="info-item">
                                        <span className="info-label">القسم:</span>
                                        <span className="info-value">{kaizen.department}</span>
                                    </div>
                                    <div className="info-item">
                                        <span className="info-label">فريق العمل:</span>
                                        <span className="info-value">{kaizen.teamMembers || "غير محدد"}</span>
                                    </div>
                                    <div className="info-item">
                                        <span className="info-label">تاريخ الإنشاء:</span>
                                        <span className="info-value">{new Date(kaizen.createdAt).toLocaleDateString('ar-EG')}</span>
                                    </div>
                                    <div className="info-item">
                                        <span className="info-label">حالة المشروع:</span>
                                        <span className={`status-badge ${kaizen.status === 'completed' ? 'status-completed' : 'status-active'}`}>
                                            {kaizen.status === 'completed' ? 'مكتمل' : 'نشط'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* خط فاصل */}
                    <div className="section-divider"></div>

                    {/* القسم الأول - المعلومات الأساسية */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                        <A3Section icon={<AlertTriangle className="text-red-600" />} title="1. بيان المشكلة">
                            <p className="text-sm">{kaizen.problemStatement}</p>
                            <div className="mt-2 text-xs text-gray-600">
                                <p><strong>القسم:</strong> {kaizen.department}</p>
                                <p><strong>فريق العمل:</strong> {kaizen.teamMembers || "غير محدد"}</p>
                            </div>
                        </A3Section>

                        <A3Section icon={<Target className="text-green-600" />} title="2. الهدف المطلوب">
                            <p className="text-sm">{kaizen.goal}</p>
                            <div className="mt-3 grid grid-cols-2 gap-2 text-center">
                                <div className="bg-red-50 p-2 rounded">
                                    <p className="font-bold text-sm text-red-700">{Number(kaizen.cost || 0).toLocaleString()}</p>
                                    <p className="text-xs text-red-600">التكلفة</p>
                                </div>
                                <div className="bg-green-50 p-2 rounded">
                                    <p className="font-bold text-sm text-green-700">{Number(kaizen.returnOnInvestment || 0).toLocaleString()}</p>
                                    <p className="text-xs text-green-600">العائد</p>
                                </div>
                            </div>
                        </A3Section>
                    </div>

                    {/* القسم الثاني - تحليل الأسباب الجذرية */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <A3Section icon={<BrainCircuit className="text-purple-600" />} title="3. تحليل الأسباب الجذرية (5 Whys)">
                            <div className="space-y-1">
                                {(kaizen.rootCauseAnalysis?.fiveWhys || []).filter(why => why.trim()).map((why, i) => (
                                    <div key={i} className="text-xs bg-gray-50 p-2 rounded">
                                        <strong>لماذا {i + 1}؟</strong> {why}
                                    </div>
                                ))}
                                {(!kaizen.rootCauseAnalysis?.fiveWhys || kaizen.rootCauseAnalysis.fiveWhys.filter(why => why.trim()).length === 0) && (
                                    <p className="text-xs text-gray-500">لم يتم إجراء تحليل 5 Whys بعد</p>
                                )}
                            </div>
                        </A3Section>

                        <A3Section icon={<BarChart2 className="text-orange-600" />} title="4. تحليل هيكل السمكة">
                            <div className="grid grid-cols-2 gap-2 text-xs">
                                {Object.entries(kaizen.rootCauseAnalysis?.fishbone || {}).map(([category, causes]) => {
                                    const categoryNames = {
                                        man: 'العاملين', machine: 'الآلات', method: 'الطرق',
                                        material: 'المواد', measurement: 'القياس', environment: 'البيئة'
                                    };
                                    return (
                                        <div key={category} className="bg-gray-50 p-2 rounded">
                                            <p className="font-bold text-xs mb-1">{categoryNames[category]}</p>
                                            {(causes || []).filter(c => c.trim()).map((cause, i) => (
                                                <p key={i} className="text-xs text-gray-700">• {cause}</p>
                                            ))}
                                            {(!causes || causes.filter(c => c.trim()).length === 0) && (
                                                <p className="text-xs text-gray-400">لا توجد أسباب</p>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                        </A3Section>
                    </div>

                    {/* القسم الثالث - الحلول والنتائج */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <A3Section icon={<ListChecks className="text-blue-600" />} title="5. الإجراءات التنفيذية (DO)">
                            <div className="space-y-2">
                                {/* الإجراءات التصحيحية */}
                                <div>
                                    <p className="text-xs font-bold mb-1">الإجراءات المقترحة:</p>
                                    <div className="space-y-1">
                                        {(kaizen.countermeasures || []).filter(c => c.trim()).map((c, i) => (
                                            <div key={i} className="text-xs bg-blue-50 p-2 rounded flex items-start gap-2">
                                                <span className="bg-blue-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold">{i + 1}</span>
                                                <span>{c}</span>
                                            </div>
                                        ))}
                                        {(!kaizen.countermeasures || kaizen.countermeasures.filter(c => c.trim()).length === 0) && (
                                            <p className="text-xs text-gray-500">لم يتم تحديد إجراءات تصحيحية بعد</p>
                                        )}
                                    </div>
                                </div>

                                {/* المهام المنجزة */}
                                <div>
                                    <p className="text-xs font-bold mb-1">المهام المنجزة:</p>
                                    <div className="space-y-1">
                                        {(kaizen.tasks || []).filter(t => t.status === 'done').map((task, i) => (
                                            <div key={i} className="text-xs bg-green-50 p-2 rounded">
                                                <p className="font-medium">✅ {task.text}</p>
                                                {task.completedBy && (
                                                    <p className="text-green-700 mt-1">👤 المنفذ: {task.completedBy}</p>
                                                )}
                                                {task.completedAt && (
                                                    <p className="text-gray-600">📅 تاريخ الإنجاز: {new Date(task.completedAt).toLocaleDateString('ar-EG')}</p>
                                                )}
                                            </div>
                                        ))}
                                        {(!kaizen.tasks || kaizen.tasks.filter(t => t.status === 'done').length === 0) && (
                                            <p className="text-xs text-gray-500">لا توجد مهام منجزة بعد</p>
                                        )}
                                    </div>
                                </div>

                                {/* المهام قيد التنفيذ */}
                                {(kaizen.tasks || []).filter(t => t.status === 'doing').length > 0 && (
                                    <div>
                                        <p className="text-xs font-bold mb-1">المهام قيد التنفيذ:</p>
                                        <div className="space-y-1">
                                            {kaizen.tasks.filter(t => t.status === 'doing').map((task, i) => (
                                                <div key={i} className="text-xs bg-yellow-50 p-2 rounded">
                                                    <p className="font-medium">⏳ {task.text}</p>
                                                    {task.assignedTo && (
                                                        <p className="text-yellow-700 mt-1">👤 المسؤول: {task.assignedTo}</p>
                                                    )}
                                                    {task.dueDate && (
                                                        <p className="text-gray-600">📅 موعد الاستحقاق: {new Date(task.dueDate).toLocaleDateString('ar-EG')}</p>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </A3Section>

                        <A3Section icon={<ShieldCheck className="text-teal-600" />} title="6. النتائج والتوحيد القياسي">
                            <div className="space-y-2">
                                <div>
                                    <p className="text-xs font-bold mb-1">النتائج:</p>
                                    <p className="text-xs bg-green-50 p-2 rounded">{kaizen.results || "لم يتم تسجيل النتائج بعد"}</p>
                                </div>
                                <div>
                                    <p className="text-xs font-bold mb-1">التوحيد القياسي:</p>
                                    <p className="text-xs bg-yellow-50 p-2 rounded">{kaizen.standardization || "لم يتم توثيق الإجراءات الجديدة بعد"}</p>
                                </div>
                            </div>
                        </A3Section>
                    </div>

                    {/* Developer Info - Footer */}
                    <div className="mt-8 pt-4 border-t border-gray-200 print:border-gray-400">
                        <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-2">
                                <Code size={12} />
                                <span>Code by: <strong>Eng. Kareem Waheeb</strong></span>
                            </div>
                            <div className="flex items-center gap-2">
                                <MessageCircle size={12} />
                                <span>WhatsApp: +20 115 929 6333</span>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <style>{`
                @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

                .a3-print-container, #a3-report-content {
                    font-family: 'Cairo', sans-serif;
                    color: #1a1a1a !important;
                    line-height: 1.6;
                }

                /* شعار الشركة - مخفي في الشاشة */
                .company-logo-header {
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 10;
                    padding: 15px;
                }

                /* إخفاء الشعار في الشاشة */
                .print-only {
                    display: none;
                }

                .company-logo-container, .company-logo-placeholder {
                    text-align: center;
                    max-width: 120px;
                }

                .company-logo-img {
                    width: 100px;
                    height: 80px;
                    object-fit: contain;
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    background: white;
                    padding: 5px;
                }

                .logo-box {
                    width: 100px;
                    height: 80px;
                    background: linear-gradient(135deg, #1e40af, #3b82f6);
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    font-weight: 600;
                    text-align: center;
                    border-radius: 8px;
                    padding: 5px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }

                .company-name {
                    font-size: 10px;
                    font-weight: 600;
                    color: #374151;
                    margin-top: 5px;
                    text-align: center;
                    line-height: 1.2;
                }

                /* رأس التقرير */
                .a3-header {
                    margin-top: 0;
                    margin-bottom: 20px;
                    padding: 20px 0;
                    border-bottom: 3px solid #1e40af;
                    position: relative;
                }

                .header-content {
                    margin-right: 0; /* مساحة كاملة في الشاشة */
                }

                .report-title {
                    font-size: 28px;
                    font-weight: 700;
                    color: #1e40af;
                    margin: 0 0 10px 0;
                    text-align: center;
                }

                .problem-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #374151;
                    margin: 0 0 15px 0;
                    text-align: center;
                    background: #f8fafc;
                    padding: 10px;
                    border-radius: 6px;
                    border-right: 4px solid #1e40af;
                }

                .report-info {
                    margin-top: 15px;
                }

                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                }

                .info-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    background: #f9fafb;
                    border-radius: 4px;
                    border-right: 3px solid #e5e7eb;
                }

                .info-label {
                    font-weight: 600;
                    color: #6b7280;
                    font-size: 12px;
                }

                .info-value {
                    font-weight: 500;
                    color: #374151;
                    font-size: 12px;
                }

                .status-badge {
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 10px;
                    font-weight: 600;
                }

                .status-completed {
                    background: #dcfce7;
                    color: #166534;
                }

                .status-active {
                    background: #dbeafe;
                    color: #1d4ed8;
                }

                /* خط فاصل */
                .section-divider {
                    height: 2px;
                    background: linear-gradient(to left, #1e40af, #3b82f6, #1e40af);
                    margin: 20px 0;
                    border-radius: 1px;
                }

                /* الأقسام */
                .a3-section {
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    page-break-inside: avoid !important;
                    break-inside: avoid !important;
                    margin-bottom: 15px;
                    background: white !important;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                    overflow: hidden;
                }

                .a3-section-title {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-weight: 700;
                    font-size: 14px;
                    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
                    padding: 12px 15px;
                    border-bottom: 2px solid #e5e7eb;
                    color: #1e40af;
                }

                .a3-section-content {
                    padding: 15px;
                    font-size: 12px;
                    color: #374151;
                    line-height: 1.5;
                }

                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }

                    @page {
                        margin: 12mm;
                        size: A3 landscape;
                        orphans: 3;
                        widows: 3;
                    }

                    body {
                        background: white !important;
                        color: #1a1a1a !important;
                        font-family: 'Cairo', sans-serif !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        font-size: 11px !important;
                        line-height: 1.4 !important;
                    }

                    .print\\:hidden {
                        display: none !important;
                    }

                    /* إظهار الشعار في الطباعة فقط */
                    .print-only {
                        display: block !important;
                    }

                    .a3-print-container {
                        position: static !important;
                        width: 100% !important;
                        height: auto !important;
                        background: white !important;
                        color: #1a1a1a !important;
                    }

                    #a3-report-content {
                        margin: 0 !important;
                        padding: 8mm !important;
                        border: none !important;
                        box-shadow: none !important;
                        border-radius: 0 !important;
                        max-width: 100% !important;
                        background: white !important;
                        color: #1a1a1a !important;
                        position: relative;
                    }

                    /* شعار الشركة في الطباعة */
                    .company-logo-header {
                        position: absolute !important;
                        top: 0 !important;
                        left: 0 !important;
                        padding: 8px !important;
                        z-index: 10 !important;
                    }

                    .company-logo-img {
                        width: 80px !important;
                        height: 60px !important;
                        border: 1px solid #ccc !important;
                    }

                    .logo-box {
                        width: 80px !important;
                        height: 60px !important;
                        font-size: 8px !important;
                    }

                    .company-name {
                        font-size: 8px !important;
                        margin-top: 3px !important;
                    }

                    /* رأس التقرير في الطباعة */
                    .a3-header {
                        margin-top: 0 !important;
                        margin-bottom: 15px !important;
                        padding: 15px 0 !important;
                        border-bottom: 2px solid #1e40af !important;
                        page-break-after: avoid !important;
                    }

                    .header-content {
                        margin-right: 100px !important;
                    }

                    .report-title {
                        font-size: 22px !important;
                        color: #1e40af !important;
                        margin-bottom: 8px !important;
                    }

                    .problem-title {
                        font-size: 14px !important;
                        background: #f8fafc !important;
                        border-right: 3px solid #1e40af !important;
                        padding: 8px !important;
                        margin-bottom: 10px !important;
                    }

                    .info-grid {
                        grid-template-columns: 1fr 1fr !important;
                        gap: 6px !important;
                    }

                    .info-item {
                        padding: 5px 8px !important;
                        background: #f9fafb !important;
                        border-right: 2px solid #e5e7eb !important;
                    }

                    .info-label, .info-value {
                        font-size: 9px !important;
                    }

                    .status-badge {
                        font-size: 8px !important;
                        padding: 2px 6px !important;
                    }

                    /* خط فاصل */
                    .section-divider {
                        height: 1px !important;
                        background: #1e40af !important;
                        margin: 12px 0 !important;
                    }

                    /* الأقسام في الطباعة */
                    .a3-section {
                        border: 1px solid #666 !important;
                        border-radius: 4px !important;
                        margin-bottom: 8px !important;
                        background: white !important;
                        box-shadow: none !important;
                        page-break-inside: avoid !important;
                        break-inside: avoid !important;
                        orphans: 3 !important;
                        widows: 3 !important;
                    }

                    .a3-section-title {
                        background: #f0f2f5 !important;
                        color: #1e40af !important;
                        border-bottom: 1px solid #666 !important;
                        padding: 8px 10px !important;
                        font-size: 11px !important;
                        font-weight: 700 !important;
                        page-break-after: avoid !important;
                    }

                    .a3-section-content {
                        color: #1a1a1a !important;
                        padding: 10px !important;
                        font-size: 9px !important;
                        line-height: 1.3 !important;
                    }

                    /* الشبكة في الطباعة */
                    .grid {
                        display: grid !important;
                    }

                    .md\\:grid-cols-2 {
                        grid-template-columns: 1fr 1fr !important;
                        gap: 8px !important;
                    }

                    .md\\:grid-cols-2 > div {
                        margin-bottom: 0 !important;
                    }

                    /* النصوص في الطباعة */
                    .text-xs {
                        font-size: 8px !important;
                        line-height: 1.2 !important;
                    }

                    .text-sm {
                        font-size: 9px !important;
                        line-height: 1.3 !important;
                    }

                    .font-bold {
                        font-weight: 700 !important;
                    }

                    .font-medium {
                        font-weight: 600 !important;
                    }

                    /* المساحات في الطباعة */
                    .space-y-1 > * + * {
                        margin-top: 2px !important;
                    }

                    .space-y-2 > * + * {
                        margin-top: 4px !important;
                    }

                    .gap-2 {
                        gap: 4px !important;
                    }

                    .gap-4 {
                        gap: 6px !important;
                    }

                    .mt-1 {
                        margin-top: 2px !important;
                    }

                    .mt-2 {
                        margin-top: 4px !important;
                    }

                    .mt-3 {
                        margin-top: 6px !important;
                    }

                    .mt-4 {
                        margin-top: 8px !important;
                    }

                    .mt-6 {
                        margin-top: 10px !important;
                    }

                    .mb-1 {
                        margin-bottom: 2px !important;
                    }

                    .mb-2 {
                        margin-bottom: 4px !important;
                    }

                    .p-2 {
                        padding: 3px !important;
                    }

                    .rounded {
                        border-radius: 2px !important;
                    }

                    /* الألوان في الطباعة */
                    .bg-blue-50, .bg-green-50, .bg-yellow-50, .bg-gray-50 {
                        background-color: #f8f9fa !important;
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .text-blue-600, .text-green-700, .text-yellow-700, .text-gray-600 {
                        color: #374151 !important;
                    }

                    .text-blue-700, .text-red-700, .text-green-700 {
                        color: #1f2937 !important;
                        font-weight: 600 !important;
                    }

                    /* منع تقطيع العناصر */
                    .bg-blue-50, .bg-green-50, .bg-yellow-50, .bg-gray-50,
                    .info-item, .status-badge {
                        page-break-inside: avoid !important;
                        break-inside: avoid !important;
                    }

                    /* تحسين عرض القوائم */
                    .space-y-1, .space-y-2 {
                        page-break-inside: avoid !important;
                        break-inside: avoid !important;
                    }
                }
            `}</style>
        </div>
    );
}