// Local Storage Manager for Kaizen App
// Replaces Supabase functionality with localStorage

const STORAGE_KEYS = {
    KAIZENS: 'kaizen_app_kaizens',
    USER_PROFILE: 'kaizen_app_user_profile',
    SETTINGS: 'kaizen_app_settings'
};

// Generate unique ID for new records
const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Helper function to get data from localStorage
const getFromStorage = (key, defaultValue = null) => {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error(`Error reading from localStorage key ${key}:`, error);
        return defaultValue;
    }
};

// Helper function to save data to localStorage
const saveToStorage = (key, data) => {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error(`Error saving to localStorage key ${key}:`, error);
        return false;
    }
};

// Kaizen CRUD operations
export const kaizenOperations = {
    // Get all kaizens
    getAll: () => {
        const kaizens = getFromStorage(STORAGE_KEYS.KAIZENS, []);
        return kaizens.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    },

    // Get kaizen by ID
    getById: (id) => {
        const kaizens = getFromStorage(STORAGE_KEYS.KAIZENS, []);
        return kaizens.find(kaizen => kaizen.id === id);
    },

    // Create new kaizen
    create: (kaizenData) => {
        const kaizens = getFromStorage(STORAGE_KEYS.KAIZENS, []);
        const newKaizen = {
            id: generateId(),
            ...kaizenData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        kaizens.push(newKaizen);
        const success = saveToStorage(STORAGE_KEYS.KAIZENS, kaizens);
        
        if (success) {
            return { data: newKaizen, error: null };
        } else {
            return { data: null, error: 'Failed to save kaizen' };
        }
    },

    // Update kaizen
    update: (id, updates) => {
        const kaizens = getFromStorage(STORAGE_KEYS.KAIZENS, []);
        const index = kaizens.findIndex(kaizen => kaizen.id === id);
        
        if (index === -1) {
            return { data: null, error: 'Kaizen not found' };
        }
        
        kaizens[index] = {
            ...kaizens[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        
        const success = saveToStorage(STORAGE_KEYS.KAIZENS, kaizens);
        
        if (success) {
            return { data: kaizens[index], error: null };
        } else {
            return { data: null, error: 'Failed to update kaizen' };
        }
    },

    // Delete kaizen
    delete: (id) => {
        const kaizens = getFromStorage(STORAGE_KEYS.KAIZENS, []);
        const filteredKaizens = kaizens.filter(kaizen => kaizen.id !== id);
        
        if (filteredKaizens.length === kaizens.length) {
            return { error: 'Kaizen not found' };
        }
        
        const success = saveToStorage(STORAGE_KEYS.KAIZENS, filteredKaizens);
        
        if (success) {
            return { error: null };
        } else {
            return { error: 'Failed to delete kaizen' };
        }
    }
};

// User profile operations (simplified since no authentication)
export const userProfileOperations = {
    // Get user profile
    get: () => {
        return getFromStorage(STORAGE_KEYS.USER_PROFILE, {
            company_name: '',
            company_logo: ''
        });
    },

    // Save user profile
    save: (profileData) => {
        const success = saveToStorage(STORAGE_KEYS.USER_PROFILE, {
            ...profileData,
            updatedAt: new Date().toISOString()
        });
        
        if (success) {
            return { data: profileData, error: null };
        } else {
            return { data: null, error: 'Failed to save profile' };
        }
    }
};

// Settings operations
export const settingsOperations = {
    // Get settings
    get: () => {
        return getFromStorage(STORAGE_KEYS.SETTINGS, {
            theme: 'light',
            language: 'ar'
        });
    },

    // Save settings
    save: (settings) => {
        const success = saveToStorage(STORAGE_KEYS.SETTINGS, settings);
        
        if (success) {
            return { data: settings, error: null };
        } else {
            return { data: null, error: 'Failed to save settings' };
        }
    }
};

// Utility functions
export const utils = {
    // Clear all data (for reset functionality)
    clearAll: () => {
        Object.values(STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
    },

    // Export data (for backup)
    exportData: () => {
        const data = {};
        Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
            data[name] = getFromStorage(key);
        });
        return data;
    },

    // Import data (for restore)
    importData: (data) => {
        try {
            Object.entries(data).forEach(([name, value]) => {
                const key = STORAGE_KEYS[name];
                if (key && value) {
                    saveToStorage(key, value);
                }
            });
            return { success: true, error: null };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
};
