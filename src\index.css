body {
  margin: 0;
  font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Additional custom styles */
* {
  box-sizing: border-box;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

/* Custom input styles for better Arabic support */
input, textarea, select {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-family: 'Cairo', sans-serif;
}

input:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button styles */
button {
  cursor: pointer;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
  font-family: 'Cairo', sans-serif;
}

button:hover {
  transform: translateY(-1px);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}
