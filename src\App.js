import React, { useState, useEffect } from 'react';
import { supabase } from './supabaseClient';
import { SplashScreen, Dashboard, NewKaizenScreen, KaizenDetailScreen, A3ReportScreen, AuthScreen } from './components';

// --- Main App Component ---
export default function App() {
    const [page, setPage] = useState('dashboard');
    const [kaizens, setKaizens] = useState([]);
    const [selectedKaizen, setSelectedKaizen] = useState(null);
    const [isAuthLoading, setIsAuthLoading] = useState(true);
    const [isDataLoading, setIsDataLoading] = useState(false);
    const [user, setUser] = useState(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [kaizenToDelete, setKaizenToDelete] = useState(null);

    // --- Authentication State Listener ---
    useEffect(() => {
        console.log('🔐 Initializing Supabase authentication...');

        // Get initial session
        supabase.auth.getSession().then(({ data: { session }, error }) => {
            if (error) {
                console.error('❌ Error getting session:', error);
            } else {
                console.log('✅ Session retrieved:', session ? 'User logged in' : 'No user');
            }
            setUser(session?.user ?? null);
            setIsAuthLoading(false);
        });

        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
            console.log('🔄 Auth state changed:', event, session ? 'User logged in' : 'No user');
            setUser(session?.user ?? null);
            setIsAuthLoading(false);
        });

        return () => subscription.unsubscribe();
    }, []);

    // --- Data Fetching ---
    useEffect(() => {
        if (!user) {
            setKaizens([]);
            setIsDataLoading(false);
            return;
        }

        fetchKaizens();

        // Subscribe to real-time changes
        const subscription = supabase
            .channel('kaizens_changes')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'kaizens',
                    filter: `user_id=eq.${user.id}`
                },
                () => {
                    fetchKaizens(); // Refetch data when changes occur
                }
            )
            .subscribe();

        return () => {
            subscription.unsubscribe();
        };
    }, [user]);

    const fetchKaizens = async () => {
        if (!user) return;

        setIsDataLoading(true);
        try {
            console.log('📊 Fetching kaizens for user:', user.id);
            const { data, error } = await supabase
                .from('kaizens')
                .select('*')
                .eq('user_id', user.id)
                .order('created_at', { ascending: false });

            if (error) throw error;

            console.log('✅ Kaizens fetched:', data?.length || 0);

            // Convert snake_case to camelCase for compatibility
            const formattedKaizens = data.map(kaizen => ({
                id: kaizen.id,
                problemStatement: kaizen.problem_statement,
                wasteType: kaizen.waste_type,
                department: kaizen.department,
                teamMembers: kaizen.team_members,
                goal: kaizen.goal,
                cost: kaizen.cost,
                returnOnInvestment: kaizen.return_on_investment,
                countermeasures: kaizen.countermeasures || [],
                status: kaizen.status,
                progress: kaizen.progress,
                tasks: kaizen.tasks || [],
                rootCauseAnalysis: kaizen.root_cause_analysis || {
                    fiveWhys: [],
                    fishbone: { man: [], machine: [], method: [], material: [], measurement: [], environment: [] }
                },
                results: kaizen.results,
                standardization: kaizen.standardization,
                createdAt: kaizen.created_at,
                updatedAt: kaizen.updated_at
            }));

            setKaizens(formattedKaizens);
        } catch (error) {
            console.error('❌ Error fetching kaizens:', error);
        } finally {
            setIsDataLoading(false);
        }
    };

    // --- Navigation and Actions ---
    const navigateTo = (pageName, kaizen = null) => {
        setSelectedKaizen(kaizen);
        setPage(pageName);
    };

    const handleKaizenCreated = (kaizen) => {
        // Data will be automatically updated via real-time subscription
        navigateTo('detail', kaizen);
    };

    const handleBackToDashboard = () => {
        setPage('dashboard');
        setSelectedKaizen(null);
    };

    const handleDeleteRequest = (kaizenId) => {
        setKaizenToDelete(kaizenId);
        setShowDeleteConfirm(true);
    };

    const confirmDeleteKaizen = async () => {
        if (!kaizenToDelete || !user) return;
        try {
            console.log('🗑️ Deleting kaizen:', kaizenToDelete);
            const { error } = await supabase
                .from('kaizens')
                .delete()
                .eq('id', kaizenToDelete)
                .eq('user_id', user.id);

            if (error) throw error;

            console.log('✅ Kaizen deleted successfully');
            handleBackToDashboard();
        } catch (error) {
            console.error('❌ Error deleting kaizen:', error);
            alert('حدث خطأ أثناء الحذف.');
        } finally {
            setShowDeleteConfirm(false);
            setKaizenToDelete(null);
        }
    };

    const handleSignOut = async () => {
        try {
            console.log('🚪 Signing out...');
            const { error } = await supabase.auth.signOut();
            if (error) throw error;
            console.log('✅ Signed out successfully');
        } catch (error) {
            console.error('❌ Error signing out:', error);
        }
    };

    if (isAuthLoading) {
        return <SplashScreen />;
    }

    if (!user) {
        return <AuthScreen />;
    }

    return (
        <div className="bg-gray-100 min-h-screen font-sans text-gray-800" dir="rtl">
            <div className="max-w-md mx-auto bg-white shadow-lg min-h-screen relative printable-area">
                {page === 'dashboard' && <Dashboard kaizens={kaizens} isDataLoading={isDataLoading} navigateTo={navigateTo} user={user} onSignOut={handleSignOut} />}
                {page === 'new' && <NewKaizenScreen user={user} onKaizenCreated={handleKaizenCreated} onBack={handleBackToDashboard} />}
                {page === 'detail' && <KaizenDetailScreen kaizen={selectedKaizen} onBack={handleBackToDashboard} navigateTo={navigateTo} onDelete={handleDeleteRequest} user={user} />}
                {page === 'a3' && <A3ReportScreen kaizen={selectedKaizen} onBack={() => navigateTo('detail', selectedKaizen)} />}

                {showDeleteConfirm && (
                     <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl text-center mx-4">
                            <h2 className="text-lg font-bold mb-2">تأكيد الحذف</h2>
                            <p className="mb-4 text-gray-600">هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.</p>
                            <div className="flex justify-center gap-4">
                                <button onClick={() => setShowDeleteConfirm(false)} className="px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300">إلغاء</button>
                                <button onClick={confirmDeleteKaizen} className="px-6 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700">حذف</button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
