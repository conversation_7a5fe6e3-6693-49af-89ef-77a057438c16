import React, { useState, useEffect } from 'react';
import { kaizenOperations } from './localStorageManager';
import { SplashScreen, Dashboard, NewKaizenScreen, KaizenDetailScreen, A3ReportScreen } from './components';

// --- Main App Component ---
export default function App() {
    const [page, setPage] = useState('dashboard');
    const [kaizens, setKaizens] = useState([]);
    const [selectedKaizen, setSelectedKaizen] = useState(null);
    const [isDataLoading, setIsDataLoading] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [kaizenToDelete, setKaizenToDelete] = useState(null);

    // --- Data Loading on App Start ---
    useEffect(() => {
        console.log('📊 Loading kaizens from localStorage...');
        fetchKaizens();
    }, []);

    // --- Data Fetching Function ---

    const fetchKaizens = () => {
        setIsDataLoading(true);
        try {
            console.log('📊 Fetching kaizens from localStorage...');
            const localKaizens = kaizenOperations.getAll();
            console.log('✅ Kaizens fetched:', localKaizens.length);
            setKaizens(localKaizens);
        } catch (error) {
            console.error('❌ Error fetching kaizens:', error);
            setKaizens([]);
        } finally {
            setIsDataLoading(false);
        }
    };

    // --- Navigation and Actions ---
    const navigateTo = (pageName, kaizen = null) => {
        setSelectedKaizen(kaizen);
        setPage(pageName);
    };

    const handleKaizenCreated = (kaizen) => {
        // Refresh data from localStorage
        fetchKaizens();
        navigateTo('detail', kaizen);
    };

    const handleBackToDashboard = () => {
        setPage('dashboard');
        setSelectedKaizen(null);
    };

    const handleDeleteRequest = (kaizenId) => {
        setKaizenToDelete(kaizenId);
        setShowDeleteConfirm(true);
    };

    const confirmDeleteKaizen = () => {
        if (!kaizenToDelete) return;
        try {
            console.log('🗑️ Deleting kaizen:', kaizenToDelete);
            const { error } = kaizenOperations.delete(kaizenToDelete);

            if (error) throw new Error(error);

            console.log('✅ Kaizen deleted successfully');
            fetchKaizens(); // Refresh data
            handleBackToDashboard();
        } catch (error) {
            console.error('❌ Error deleting kaizen:', error);
            alert('حدث خطأ أثناء الحذف.');
        } finally {
            setShowDeleteConfirm(false);
            setKaizenToDelete(null);
        }
    };

    // No sign out needed since no authentication

    return (
        <div className="bg-gray-100 min-h-screen font-sans text-gray-800" dir="rtl">
            <div className="max-w-md mx-auto bg-white shadow-lg min-h-screen relative printable-area">
                {page === 'dashboard' && <Dashboard kaizens={kaizens} isDataLoading={isDataLoading} navigateTo={navigateTo} />}
                {page === 'new' && <NewKaizenScreen onKaizenCreated={handleKaizenCreated} onBack={handleBackToDashboard} />}
                {page === 'detail' && <KaizenDetailScreen kaizen={selectedKaizen} onBack={handleBackToDashboard} navigateTo={navigateTo} onDelete={handleDeleteRequest} />}
                {page === 'a3' && <A3ReportScreen kaizen={selectedKaizen} onBack={() => navigateTo('detail', selectedKaizen)} />}

                {showDeleteConfirm && (
                     <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl text-center mx-4">
                            <h2 className="text-lg font-bold mb-2">تأكيد الحذف</h2>
                            <p className="mb-4 text-gray-600">هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.</p>
                            <div className="flex justify-center gap-4">
                                <button onClick={() => setShowDeleteConfirm(false)} className="px-6 py-2 rounded-lg bg-gray-200 hover:bg-gray-300">إلغاء</button>
                                <button onClick={confirmDeleteKaizen} className="px-6 py-2 rounded-lg bg-red-600 text-white hover:bg-red-700">حذف</button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
