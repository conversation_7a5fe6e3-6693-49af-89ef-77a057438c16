# Netlify redirects for React SPA
# This ensures all routes are handled by React Router

# Redirect all routes to index.html for SPA functionality
/*    /index.html   200

# Optional: Redirect HTTP to HTTPS (uncomment if needed)
# http://kaizen-app.netlify.app/*    https://kaizen-app.netlify.app/:splat    301!

# Optional: Redirect www to non-www (uncomment if needed)
# https://www.kaizen-app.netlify.app/*    https://kaizen-app.netlify.app/:splat    301!
