# Netlify configuration file for Kaizen App

[build]
  # Build command
  command = "npm run build"
  
  # Directory to publish (build output)
  publish = "build"
  
  # Node.js version
  environment = { NODE_VERSION = "18" }

[build.environment]
  # Disable CI warnings that can cause build failures
  CI = "false"
  
  # Increase memory limit for build process
  NODE_OPTIONS = "--max-old-space-size=4096"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Cache control for static assets
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/index.html"
  [headers.values]
    # Don't cache the main HTML file
    Cache-Control = "no-cache"

[[headers]]
  for = "/static/js/*"
  [headers.values]
    # Cache JavaScript files for 1 year
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/static/css/*"
  [headers.values]
    # Cache CSS files for 1 year
    Cache-Control = "public, max-age=31536000, immutable"

# Redirect rules (backup to _redirects file)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Error pages
[[redirects]]
  from = "/404"
  to = "/index.html"
  status = 200
